import { observable, action, computed } from 'mobx';
import Session from '../../api/session';

/**
 * 公司
 */
class Stores {
  @observable companyInfo = null;
  @observable balance = 0;
  @observable totalPackage = 0;
  @observable statistics = null; // 职位状态统计
  @observable homeResumeStatistics = null; // 首页简历筛选统计
  @observable applicationsStatistics = null; // 全部简历筛选统计
  @observable deliveryStatistics = null; // 简历投递筛选统计
  @observable buyStatistics = null; // 简历已购买筛选统计
  @observable downloadStatistics = null; // 简历下载筛选统计
  @observable totalCollection = 0; // 收藏简历数

  @observable totalContacts = 0; // 总的联系人数
  @observable totalWaitCount = 0; // 待面试联系人数
  @observable totalPastCount = 0; // 已面试联系人数
  @observable totalNotCount = 0; // 未面试联系人数
  @observable commentOptions = []; // 评价选项
  @observable orderStatusList = []; // 订单状态列表
  @observable tradeTypeList = []; // 交易类型列表

  @observable industrialOptions = []; // 公司行业列表
  @observable employerScaleOptions = []; // 公司规模列表
  @observable etypeOptions = null; // 公司性质列表
  @observable locationOptions = []; // 地址区域列表
  @observable messageTotal = 0;

  @observable enableSubImAccount = false; // 是否启用子账号
  @observable imAccounts = []; // 子账号列表
  @observable selectedImAccount = null; // 选中的子账号

  @computed get employerId() {
    return this.companyInfo?.employerId;
  }

  @action
  setSelectedImAccount = async (account) => {
    this.selectedImAccount = account;
    // 保存到本地存储
    try {
      await Session.setSelectedImAccount(account, this.employerId);
    } catch (error) {
      console.error('保存选中的IM账号失败:', error);
    }
  };

  @action
  loadSelectedImAccount = async () => {
    try {
      const account = await Session.getSelectedImAccount(this.employerId);
      this.selectedImAccount = account;
      console.log('加载选中的IM账号成功:', account);
      return account;
    } catch (error) {
      console.log('加载选中的IM账号失败:', error);
      this.selectedImAccount = null;
      return null;
    }
  };

  @action
  reset = () => {
    this.companyInfo = null;
    this.balance = 0;
    this.totalPackage = 0;
    this.statistics = null;
    this.applicationsStatistics = null;
    this.deliveryStatistics = null;
    this.buyStatistics = null;
    this.downloadStatistics = null;
    this.totalCollection = 0;
    this.totalContacts = 0;
    this.totalWaitCount = 0;
    this.totalPastCount = 0;
    this.totalNotCount = 0;
    this.commentOptions = [];
    this.orderStatusList = [];
    this.tradeTypeList = [];
    this.homeResumeStatistics = null;
    this.messageTotal = 0;
    this.enableSubImAccount = false;
    this.imAccounts = [];
    this.selectedImAccount = null;
  };
}

const st = new Stores();
st.reset();

export default st;
