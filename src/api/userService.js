import Restful from './restful';
import qs from 'qs';
import Session from './session';
import userStore from '../store/stores/user';
import companyStore from '../store/stores/company';

import DeviceInfo from '../util/deviceInfo';
import PushUtil from '../util/pushUtil';
import { getAcceptLanguageByIM } from '../i18n';
import configs from '../configs';

class UserService {
  /**
   * 设置语言
   * @param {*} language
   */
  async setLanguage(language) {
    return Restful.put(`/users/language?language=${language}`, { language });
  }

  async setEmployersLanguage(language) {
    return Restful.put(`/employers/language?language=${language}`, { language });
  }

  /**
   * 获取用户详情
   */
  async getCurrUserInfo() {
    return Restful.get('/users/me');
  }

  /**
   * 获取用户详情根据用户id
   */
  async getUserBySeekerId(seekerId) {
    return Restful.get(`/users/${seekerId}`);
  }

  /**
   * 修改密码
   * @param {*} code          验证码
   * @param {*} newPassword   新密码
   * @param {*} oldPassword   旧密码
   */
  async modifyPassword({ code, newPassword, oldPassword }) {
    return Restful.put('/users/password', { code, newPassword, oldPassword });
  }

  /**
   * 修改密码短信验证码
   */
  async sendSmsCode4ModifyPassword() {
    return Restful.get('/users/password/smsCode');
  }

  /**
   * 我的页面修改密码
   * @param {*} newPassword   新密码
   * @param {*} oldPassword   旧密码
   */
  async generalModifyPassword(newPassword, oldPassword) {
    return Restful.put('/users/password', { newPassword, oldPassword });
  }

  /**
   * 我的页面修改手机号
   * @param {*} newMobile          新手机号
   * @param {*} regionCode         区域码
   * @param {*} verificationCode   验证码
   */
  async generalModifyPhone(data) {
    return Restful.put('/users/mobile', data);
  }

  /**
   * 用户收藏公司
   * request: /v1.0.0/users/employers/{employerId}/following
   * method: POST
   * @param employerId - employerId
   */
  async followCompany(employerId) {
    return Restful.post(`/users/employers/${employerId}/following`);
  }

  /**
   * 查询收藏的公司
   * request: /v1.0.0/users/employers/following
   * method: GET
   * @param param - param
   */
  async queryFollowedCompanys(param) {
    return Restful.get(
      `/users/employers/following?${qs.stringify(param, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 删除收藏的公司
   * request: /v1.0.0/users/employers/following
   * method: DELETE
   * @param employerIds - employerIds
   */
  async deleteFollowedCompany(employerIds) {
    return Restful.delete(
      `/users/employers/following?${qs.stringify(employerIds, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 用户收藏职位
   * request: /v1.0.0/users/jobs/{jobId}/following
   * method: POST
   * @param jobId - jobId
   */
  async followJob(jobId) {
    return Restful.post(`/users/jobs/${jobId}/following`);
  }

  /**
   * 查询收藏的职位
   * request: /v1.0.0/users/jobs/following
   * method: GET
   * @param param - param
   */
  async queryFollowedJobs(param) {
    return Restful.get(`/users/jobs/following?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 删除收藏的职位
   * request: /v1.0.0/users/jobs/following
   * method: DELETE
   * @param jobIds - jobIds
   */
  async deleteFollowedJob(jobIds) {
    return Restful.delete(
      `/users/jobs/following?${qs.stringify(jobIds, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 上传上传
   * request: /v1.0.0/users/avatar
   * method: POST
   * @param file - file
   */
  async uploadImage(file) {
    return Restful.upload('/users/avatar', file);
  }

  /**
   * 邀请面试消息确认接口（接受或拒绝）
   * request: /v1.0.0/users/applications/{jobApplyId}/confirm
   * method: POST
   * @param jobApplyId - jobApplyId
   */
  async postInterviewConfirm(jobApplyId, data) {
    return Restful.post(`/users/applications/${jobApplyId}/confirm`, data);
  }

  /**
   * 获取面试详情
   * request: /v1.0.0/users/applications/{jobApplyId}
   * method: GET
   * @param jobApplyId - jobApplyId
   */
  async getInterviewDetail(jobApplyId) {
    return Restful.get(`/users/applications/${jobApplyId}`);
  }

  /**
   * 用户反馈
   * request: /v1.0.0/users/feedbacks
   * method: POST
   * @param feedback - feedback
   */
  async userFeedback(feedback) {
    return Restful.post('/users/feedbacks', feedback);
  }

  /**
   * 获取首页的轮播图
   */
  async getBanners() {
    return Restful.get('/users/slideshow');
  }

  /**
   * 设置默认语言
   */
  async setDefaultLanguage(language) {
    return Restful.put(`/users/language?language=${language}`);
  }

  /**
   * 上传设备信息
   * @param status 2为已登录 3为退出登录
   */
  async uploadDeviceInfo(status) {
    try {
      const device = await this.getDeviceInfo(status);
      return await Restful.put(
        device.accountType ? '/users/devices' : '/employers/devices',
        device
      );
    } catch (e) {
      console.warn('uploadDeviceInfo error', e);
    }
  }

  // eslint-disable-next-line max-len
  /** *********************************   通知消息处理接口   ************************************* */
  /**
   * 查询消息
   * @param {*} param
   */
  async queryMessageInbox(param) {
    return Restful.get(`/users/messages/inbox?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 获取系统消息详情
   * @messageId {*} messageId
   */
  async getMessageDetail(messageId) {
    return Restful.get(`/users/messages/${messageId}`);
  }

  /**
   * 获取公开系统消息详情
   * @messageId {*} messageId
   */
  async getPublicMessageDetail(messageId) {
    return Restful.get(`/users/messages/public/${messageId}`);
  }

  /**
   * 标记消息为已读
   * @param {*} param
   */
  async markMessageRead(messageId) {
    return Restful.put(`/users/messages/${messageId}`);
  }

  /**
   * 统计系统消息
   * @param {*} param
   */
  async statsMessage() {
    return Restful.get('/users/messages/statistics');
  }

  /** ****************  动态相关新需求  *********************** */

  /**
   * 统计动态未读的新消息
   * @param {*} null
   */
  async getUnreadTwitterMessages() {
    return Restful.get('/users/twitters/unread/message/statistics');
  }

  /**
   * 批量标记动态消息为已读
   * @param {*} messageIds
   */
  async markTwitterMessagesRead() {
    return Restful.put('/users/messages/inbox/read/twitter');
  }

  /**
   * 清空动态相关的消息提醒
   * @param {*} messageIds
   */
  async deleteAllTwitterMessages() {
    return Restful.delete('/users/messages/inbox/twitter/all');
  }

  /**
   * 删除单条动态相关的消息提醒
   * @param {*} messageId
   */
  async deleteTwitterMessage(messageId) {
    return Restful.delete(`/users/messages/${messageId}/inbox`);
  }

  /**
   * 获取某个动态的点赞人信息
   * @param {*} twitterId
   */
  async getTwitterLikedUsers(twitterId) {
    return Restful.get(`/users/twitters/${twitterId}/liker`);
  }

  /**
   * 获取动态详情
   * @param {*} twitterId
   */
  async getTwitterDetail(twitterId) {
    return Restful.get(`/users/twitters/${twitterId}`);
  }

  /**
   * 获取动态新消息提醒列表
   * @param {*} param
   */
  async queryTwitterMessage(param) {
    return Restful.get(`/users/twitters/message?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 获取启动页广告图片
   */
  async getLaunchAdvImages() {
    return Restful.get('/users/slideshow/app/advertisement');
  }

  /**
   * 扫码登录
   * @param {*}
   */
  async scanLogin(queryString) {
    return Restful.post(`/passport/qrcode/authorize?${queryString}`);
  }

  /**
   * 组装设备信息
   * @param status 2为已登录 3为退出登录
   */
  getDeviceInfo = async (status) => {
    const { userId, isCompany } = userStore;
    if (!userId) {
      throw Error('not login');
    }
    const deviceName = await DeviceInfo.getDeviceName();
    const mac = await DeviceInfo.getMacAddress();
    const pushId = await PushUtil.getRegistrationID();
    return {
      udid: DeviceInfo.getUniqueID(),
      platform: IS_IOS ? 1 : 2,
      status,
      userId,
      deviceBrand: DeviceInfo.getBrand(),
      deviceName,
      deviceModel: DeviceInfo.getModel(),
      osVersion: DeviceInfo.getSystemVersion(),
      deviceMac: mac,
      pushId,
      accountType: isCompany ? 0 : 1,
      version: DeviceInfo.getVersion(),
    };
  };

  /**
   * IM登录
   */
  async imLogin(isCompany) {
    const device = await this.getDeviceInfo(2);
    const data = { device };
    // if (companyStore.selectedImAccount) {
    //   data.account = companyStore.selectedImAccount;
    //   data.password = companyStore.selectedImPassword;
    // }
    return Restful.post(isCompany ? '/employers/authorize/im' : '/users/authorize/im', data, {
      headers: {
        'Accept-Language': getAcceptLanguageByIM(),
      },
      showToast: false,
    });
  }

  /**
   * 企业IM登录
   */
  async imLoginByEmployee(params) {
    const device = await this.getDeviceInfo(2);
    const data = { device, ...params };
    return Restful.post('/employers/authorize/im', data, {
      headers: {
        'Accept-Language': getAcceptLanguageByIM(),
      },
      showToast: false,
    });
  }

  /**
   * 根据企业/求职者查询对应的聊天信息
   */
  async getImInfo(param) {
    const imToken = await Session.getImAccessToken();
    const config = {
      headers: {
        'x-im-token': imToken?.token,
      },
    };
    return Restful.get('/camhr/im/users/details', param, config);
  }

  //获取图像验证码
  getImageCaptchaUrl(param) {
    param.t = Date.now();
    return `${configs.serverURL}/users/sms/image-captcha?${qs.stringify(param, {
      arrayFormat: 'repeat',
    })}`;
  }

  // 查询新职位提醒消息
  async queryNewJobMessage(param) {
    console.log('queryNewJobMessage param', param);
    return Restful.get(
      `/users/notify/msg/new-job?${qs.stringify(param, { arrayFormat: 'repeat' })}`
    );
  }

  // 查询新职位提醒消息未读数量
  async queryNewJobMessageBadge() {
    return Restful.get('/users/notify/msg/new-job/badge');
  }

  // 清空未读数量
  async clearNewJobMessageBadge() {
    return Restful.delete('/users/notify/msg/new-job/badge');
  }
}

export default new UserService();
