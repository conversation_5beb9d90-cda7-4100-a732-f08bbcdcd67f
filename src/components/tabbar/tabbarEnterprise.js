import React, { Component } from 'react';
import { Image, StatusBar, View, Linking } from '../index';
import { inject, observer } from 'mobx-react';
import TabNavigator from 'react-native-tab-navigator';
import I18n from '../../i18n';
import res from '../../res';
import styles from '../../themes/enterprise';
import Home from '../../pages/enterprise/home/<USER>';
import Resume from '../../pages/enterprise/resume/resume';
import Job from '../../pages/enterprise/job/job';
import ServicePackage from '../../pages/enterprise/servicePackage/servicePackage';
import constant from '../../store/constant';
import DeepLinkService from '../../deepLinkService';
import ChatEnterprise from '../../pages/chat/chatEnterprise';
import SelectJobModal from '../../pages/enterprise/resume/components/selectJobModal';
import pageAction from '../../store/actions/page';
import SubImLoginModal from '../modal/SubImLoginModal';

/**
 * 企业端底部tabbar页面
 * <AUTHOR>
 */
@inject('homeAction', 'pageStore', 'mineAction', 'companyAction', 'stores')
@observer
export default class TabbarEnterprise extends Component {
  tabbarStyle = styles.get('tabbar');

  componentDidMount() {
    this.willFocusSubscription = this.props.navigation.addListener('willFocus', this.willFocus);
    global.emitter.on(constant.event.sessionTimeout, this.handleSessionTimeout);
    global.emitter.on(constant.event.imConfigChange, this.handleImConfigChange);
    Linking.getInitialURL()
      .then((url) => {
        if (url) {
          DeepLinkService.handleOpenURL(url);
        }
      })
      .catch((err) => console.log('An error occurred', err));
  }

  componentWillUnmount() {
    this.willFocusSubscription.remove();
    global.emitter.off(constant.event.sessionTimeout, this.handleSessionTimeout);
    global.emitter.off(constant.event.imConfigChange, this.handleImConfigChange);
  }

  handleSessionTimeout = () => {};

  handleImConfigChange = async () => {
    // 当IM配置变化时，检查是否需要显示子账号登录弹框
    const { companyStore } = this.props.stores;
    if (companyStore.enableSubImAccount && !companyStore.selectedImAccount) {
      // 只有启用了子账号功能且没有选中的账号时才弹出登录框
      // 延迟一点时间显示，确保页面已经渲染完成
      setTimeout(() => {
        this.subImLoginModal.wrappedInstance.show();
      }, 500);
    }
  };

  willFocus = async () => {
    console.log('TabBar willFocus');
    await this.props.homeAction.getData();
    await this.props.companyAction.getEmployerImConfig();
  };

  /**
   * 切换tab清除定时器
   */
  selectedTab = (name) => {
    pageAction.selectedTab(name);
  };

  selectedTab1 = () => this.selectedTab(constant.tabs.home);

  selectedTab2 = () => this.selectedTab(constant.tabs.resume);

  selectedTab3 = () => this.selectedTab(constant.tabs.chat);

  selectedTab4 = () => this.selectedTab(constant.tabs.job);

  selectedTab5 = () => this.selectedTab(constant.tabs.mine);

  renderIcon = (icon) => <Image source={icon} style={this.tabbarStyle.icon} resizeMode="contain" />;

  renderIcon1 = () => this.renderIcon(res.tabbarHome);

  renderIcon2 = () => this.renderIcon(res.tabbarResume);

  renderIcon3 = () => this.renderIcon(res.tabbarChat);

  renderIcon4 = () => this.renderIcon(res.tabbarWork);

  renderIcon5 = () => this.renderIcon(res.tabbarForum);

  renderSelectedIcon = (icon) => (
    <Image source={icon} style={this.tabbarStyle.selectedIcon} resizeMode="contain" />
  );

  renderSelectedIcon1 = () => this.renderSelectedIcon(res.tabbarHomeActive);

  renderSelectedIcon2 = () => this.renderSelectedIcon(res.tabbarResumeActive);

  renderSelectedIcon3 = () => this.renderSelectedIcon(res.tabbarChatActive);

  renderSelectedIcon4 = () => this.renderSelectedIcon(res.tabbarWorkActive);

  renderSelectedIcon5 = () => this.renderSelectedIcon(res.tabbarForumActive);

  render() {
    const { tabbarStyle, props } = this;
    const { navigation, pageStore } = props;
    const { selectedTab } = pageStore;
    return (
      <View style={{ flex: 1 }}>
        <StatusBar
          containerStyle={{ height: 0 }}
          barStyle={selectedTab === constant.tabs.home ? 'light-content' : 'dark-content'}
        />
        <TabNavigator
          style={tabbarStyle.container}
          tabBarStyle={tabbarStyle.tabBar}
          tabBarShadowStyle={tabbarStyle.tabBarShadowStyle}
          sceneStyle={tabbarStyle.tabBarScens}
        >
          <TabNavigator.Item
            selected={selectedTab === constant.tabs.home}
            title={I18n.t('page_tabbar_text_home_title')}
            titleStyle={tabbarStyle.title}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={this.renderIcon1}
            renderSelectedIcon={this.renderSelectedIcon1}
            onPress={this.selectedTab1}
          >
            <Home navigation={navigation} />
          </TabNavigator.Item>
          <TabNavigator.Item
            selected={selectedTab === constant.tabs.resume}
            title={I18n.t('menu_nav_bottom_resume')}
            titleStyle={tabbarStyle.title}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={this.renderIcon2}
            renderSelectedIcon={this.renderSelectedIcon2}
            onPress={this.selectedTab2}
          >
            <Resume navigation={navigation} />
          </TabNavigator.Item>
          <TabNavigator.Item
            selected={selectedTab === constant.tabs.chat}
            title={I18n.t('page_tabbar_text_message_title')}
            titleStyle={tabbarStyle.title}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={this.renderIcon3}
            renderSelectedIcon={this.renderSelectedIcon3}
            renderBadge={() => {
              return this.props.msgBadge ? <View style={tabbarStyle.badge} /> : null;
            }}
            onPress={this.selectedTab3}
          >
            <ChatEnterprise navigation={navigation} />
          </TabNavigator.Item>
          <TabNavigator.Item
            selected={selectedTab === constant.tabs.job}
            title={I18n.t('menu_nav_bottom_job')}
            titleStyle={tabbarStyle.title}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={this.renderIcon4}
            renderSelectedIcon={this.renderSelectedIcon4}
            onPress={this.selectedTab4}
          >
            <Job navigation={navigation} />
          </TabNavigator.Item>
          <TabNavigator.Item
            selected={selectedTab === constant.tabs.mine}
            title={I18n.t('page_tabbar_text_service_package_title')}
            titleStyle={tabbarStyle.title}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={this.renderIcon5}
            renderSelectedIcon={this.renderSelectedIcon5}
            onPress={this.selectedTab5}
          >
            <ServicePackage navigation={navigation} />
          </TabNavigator.Item>
        </TabNavigator>
        <SelectJobModal page="main" title="" />

        {/* 登录IM子账号弹出框 */}
        <SubImLoginModal ref={(ref) => (this.subImLoginModal = ref)} />
      </View>
    );
  }
}
