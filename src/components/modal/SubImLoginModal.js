import React, { Component } from 'react';
import { View, Text, TouchableOpacity, TextInput } from 'react-native';
import { inject, observer } from 'mobx-react';
import BottomModal from './bottomModal';
import { Button } from '../index';

import styles from '../../themes/enterprise';
import SelectContactModal from './SelectContactModal';
import chatAction from '../../store/actions/chatAction';
import uiUtil from '../../util/uiUtil';

function getComponentStyle(theme) {
  return {
    container: {
      paddingHorizontal: 20,
      paddingBottom: 15,
    },
    inputRow: {
      marginBottom: 20,
    },
    label: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 8,
      fontWeight: theme.fontWeightMedium,
    },
    input: {
      height: 44,
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: 4,
      paddingHorizontal: 12,
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      backgroundColor: '#fff',
    },
    selectButton: {
      height: 44,
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: 4,
      paddingHorizontal: 12,
      justifyContent: 'center',
      backgroundColor: '#fff',
    },
    selectButtonText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    selectButtonPlaceholder: {
      fontSize: theme.fontSizeM,
      color: '#999',
    },
    radioRow: {
      marginBottom: 20,
    },
    radioGroup: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    radioOption: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 30,
    },
    radioButton: {
      width: 18,
      height: 18,
      borderRadius: 9,
      borderWidth: 2,
      borderColor: '#E5E5E5',
      marginRight: 8,
      justifyContent: 'center',
      alignItems: 'center',
    },
    radioButtonSelected: {
      borderColor: theme.stressColor,
    },
    radioButtonInner: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.stressColor,
    },
    radioText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 10,
    },
    button: {
      flex: 1,
      marginHorizontal: 5,
    },
  };
}

/**
 * 登录IM子账号弹出框组件
 * <AUTHOR>
 */
@inject('stores')
@observer
export default class SubImLoginModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isVisible: false,
      selectedContact: null,
      password: '',
    };
  }

  show = (data = {}) => {
    // 如果没有传入选中的联系人，尝试从store获取当前选中的账号
    const { companyStore } = this.props.stores;
    const defaultContact = data.selectedContact || companyStore.selectedImAccount;

    this.setState({
      isVisible: true,
      selectedContact: defaultContact,
      password: data.password || '',
    });
  };

  hide = () => {
    this.setState({ isVisible: false });
  };

  onSelectContact = () => {
    this.selectContactModal.wrappedInstance.show();
  };

  onContactSelected = (contact) => {
    this.setState({ selectedContact: contact });
  };

  onChangePassword = (text) => {
    this.setState({ password: text });
  };

  onConfirm = async () => {
    try {
      const { selectedContact, password } = this.state;

      // 验证选择的联系人
      if (!selectedContact) {
        global.toast.show('请选择企业联系人账号');
        return;
      }

      // 验证密码
      if (!password.trim()) {
        global.toast.show('请输入密码');
        return;
      }
      uiUtil.showGlobalLoading();
      await chatAction.loginIMByEmployee({
        imUsername: selectedContact.imUsername,
        imPassword: password.trim(),
      });
      uiUtil.hideGlobalLoading();
      this.hide();
    } catch (error) {
      uiUtil.showRequestResult(error);
    }
  };

  onCancel = () => {
    chatAction.loginIM();
    this.hide();
  };

  render() {
    const { isVisible, selectedContact, password } = this.state;
    const { themeStyle } = styles.get(['theme']);
    const style = getComponentStyle(themeStyle);
    const contentHeight = 240;

    return (
      <>
        <BottomModal
          ref={(ref) => (this.modal = ref)}
          backdropPressToClose
          title="登录IM子账号"
          showCancel={false}
          contentHeight={contentHeight}
          keyboardShouldPersistTaps="always"
          onClosed={this.hide}
          isOpen={isVisible}
          useScrollContent={false}
          showBottomView={false}
        >
          <View style={style.container}>
            {/* 企业联系人账号选择 */}
            <View style={style.inputRow}>
              <Text style={style.label}>企业联系人账号:</Text>
              <TouchableOpacity style={style.selectButton} onPress={this.onSelectContact}>
                <Text
                  style={selectedContact ? style.selectButtonText : style.selectButtonPlaceholder}
                >
                  {selectedContact ? selectedContact.imUsername : '请选择联系人账号'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* 密码输入 */}
            <View style={style.inputRow}>
              <Text style={style.label}>密码:</Text>
              <TextInput
                ref={(ref) => (this.passwordInput = ref)}
                style={style.input}
                placeholder="请输入密码"
                value={password}
                onChangeText={this.onChangePassword}
                secureTextEntry={true}
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="done"
                selectionColor="#EF3D48"
                onSubmitEditing={this.onConfirm}
                maxLength={31}
                placeholderTextColor="#999"
              />
            </View>

            {/* 按钮组 */}
            <View style={style.buttonContainer}>
              <Button
                title="取消"
                onPress={this.onCancel}
                btnSize="md"
                outline
                btnType="reset"
                containerStyle={style.button}
              />
              <Button
                title="确认"
                onPress={this.onConfirm}
                btnSize="md"
                btnType="primary"
                containerStyle={style.button}
              />
            </View>
          </View>
        </BottomModal>

        {/* 选择联系人弹出框 */}
        <SelectContactModal
          ref={(ref) => (this.selectContactModal = ref)}
          onConfirm={this.onContactSelected}
        />
      </>
    );
  }
}
