<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>下载应用</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .container {
      background: white;
      border-radius: 12px;
      padding: 30px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      text-align: center;
      max-width: 400px;
      width: 100%;
    }

    .loading {
      display: block;
    }

    .error {
      display: none;
    }

    .spinner {
      border: 3px solid #f3f3f3;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .btn {
      display: inline-block;
      padding: 12px 24px;
      margin: 10px;
      background: #667eea;
      color: white;
      text-decoration: none;
      border-radius: 6px;
      transition: background 0.3s;
    }

    .btn:hover {
      background: #5a6fd8;
    }

    .btn-secondary {
      background: #6c757d;
    }

    .btn-secondary:hover {
      background: #5a6268;
    }

    h2 {
      color: #333;
      margin-bottom: 20px;
    }

    p {
      color: #666;
      line-height: 1.6;
    }
  </style>
  <script>
    var redirectTimeout;
    var fallbackTimeout;

    function showError() {
      document.querySelector('.loading').style.display = 'none';
      document.querySelector('.error').style.display = 'block';
    }

    function tryAppStore(url, webUrl) {
      // 尝试打开应用商店
      window.location.href = url;

      // 设置超时，如果3秒后页面还在，说明可能打开失败
      fallbackTimeout = setTimeout(function () {
        showError();
      }, 3000);

      // 监听页面可见性变化，如果用户切换到其他应用，清除超时
      document.addEventListener('visibilitychange', function () {
        if (document.hidden) {
          clearTimeout(fallbackTimeout);
        }
      });

      // 监听页面失焦，可能是跳转到了应用商店
      window.addEventListener('blur', function () {
        clearTimeout(fallbackTimeout);
      });
    }

    function openWebVersion(url) {
      window.open(url, '_blank');
    }

    window.onload = function () {
      var ua = navigator.userAgent || navigator.vendor || window.opera;

      if (/android/i.test(ua)) {
        // Android设备
        tryAppStore(
          "https://play.google.com/store/apps/details?id=com.camhr.app",
          "https://play.google.com/store/apps/details?id=com.camhr.app"
        );
      } else if (/iPad|iPhone|iPod/.test(ua) && !window.MSStream) {
        // iOS设备
        tryAppStore(
          "https://itunes.apple.com/cn/app/qq/id1442800498?mt=8",
          "https://apps.apple.com/cn/app/qq/id1442800498"
        );
      } else {
        // 其他设备，直接跳转到下载页面
        window.location.href = "http://fir.mosainet.com/camhrforandroid";
      }
    };
  </script>
</head>

<body>
  <div class="container">
    <div class="loading">
      <div class="spinner"></div>
      <h2>正在跳转应用商店...</h2>
      <p>请稍候，正在为您打开应用下载页面</p>
    </div>

    <div class="error">
      <h2>无法打开应用商店？</h2>
      <p>如果应用商店无法正常打开，您可以尝试以下方式：</p>

      <div id="android-options" style="display: none;">
        <a href="https://play.google.com/store/apps/details?id=com.camhr.app" target="_blank" class="btn">打开网页版 Google
          Play</a>
        <a href="http://fir.mosainet.com/camhrforandroid" target="_blank" class="btn btn-secondary">直接下载 APK</a>
      </div>

      <div id="ios-options" style="display: none;">
        <a href="https://apps.apple.com/cn/app/qq/id1442800498" target="_blank" class="btn">打开网页版 App Store</a>
      </div>

      <div id="other-options" style="display: none;">
        <a href="http://fir.mosainet.com/camhrforandroid" target="_blank" class="btn">下载 Android 版本</a>
      </div>

      <script>
        // 根据设备类型显示对应的选项
        var ua = navigator.userAgent || navigator.vendor || window.opera;
        if (/android/i.test(ua)) {
          document.getElementById('android-options').style.display = 'block';
        } else if (/iPad|iPhone|iPod/.test(ua) && !window.MSStream) {
          document.getElementById('ios-options').style.display = 'block';
        } else {
          document.getElementById('other-options').style.display = 'block';
        }
      </script>
    </div>
  </div>
</body>

</html>